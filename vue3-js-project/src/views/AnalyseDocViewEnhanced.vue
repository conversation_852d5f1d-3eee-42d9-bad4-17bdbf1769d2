<template>
  <div class="analyse-doc-container">
    <a-card title="Excel数据分析增强版" class="analyse-card">
      <!-- 功能导航栏 -->
      <a-space class="feature-nav" style="margin-bottom: 20px">
        <a-button type="primary" @click="showHistory = !showHistory">
          <history-outlined />
          历史记录
        </a-button>
        <a-button type="primary" @click="showTemplates = !showTemplates">
          <folder-outlined />
          分析模板
        </a-button>
        <a-button type="primary" @click="showCharts = !showCharts">
          <bar-chart-outlined />
          图表可视化
        </a-button>
        <a-button type="primary" @click="showBatch = !showBatch">
          <upload-outlined />
          批量分析
        </a-button>
      </a-space>

      <a-row :gutter="24">
        <!-- 左侧主面板 -->
        <a-col :span="showCharts ? 16 : 24">
          <a-form layout="vertical">
            <!-- 文件上传区域 -->
            <a-form-item label="上传Excel文件">
              <a-upload-dragger
                v-model:fileList="fileList"
                :multiple="enableBatchUpload"
                :before-upload="beforeUpload"
                :remove="handleRemove"
                accept=".xlsx,.xls"
                :max-count="enableBatchUpload ? 5 : 1"
              >
                <p class="ant-upload-drag-icon">
                  <inbox-outlined />
                </p>
                <p class="ant-upload-text">点击或拖拽文件到此处上传</p>
                <p class="ant-upload-hint">
                  支持单个或批量上传Excel文件，每个文件不超过10MB
                </p>
              </a-upload-dragger>
            </a-form-item>

            <!-- 分析模板选择 -->
            <a-form-item label="选择分析模板" v-if="showTemplates">
              <a-select v-model:value="selectedTemplate" style="width: 100%" @change="applyTemplate">
                <a-select-option value="">自定义分析</a-select-option>
                <a-select-option value="sales">销售数据分析</a-select-option>
                <a-select-option value="finance">财务报表分析</a-select-option>
                <a-select-option value="hr">人力资源分析</a-select-option>
                <a-select-option value="inventory">库存管理分析</a-select-option>
                <a-select-option value="customer">客户行为分析</a-select-option>
              </a-select>
            </a-form-item>

            <!-- 分析类型和配置 -->
            <a-row :gutter="16">
              <a-col :span="12">
                <a-form-item label="分析类型">
                  <a-select v-model:value="analysisType" style="width: 100%">
                    <a-select-option value="general">通用分析</a-select-option>
                    <a-select-option value="trend">趋势分析</a-select-option>
                    <a-select-option value="statistics">统计分析</a-select-option>
                    <a-select-option value="correlation">相关性分析</a-select-option>
                    <a-select-option value="anomaly">异常检测</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="图表类型">
                  <a-select v-model:value="chartType" style="width: 100%">
                    <a-select-option value="bar">柱状图</a-select-option>
                    <a-select-option value="line">折线图</a-select-option>
                    <a-select-option value="pie">饼图</a-select-option>
                    <a-select-option value="scatter">散点图</a-select-option>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- API配置 -->
            <a-form-item>
              <a-button type="link" @click="showSettings = !showSettings">
                <setting-outlined />
                API配置
              </a-button>
            </a-form-item>

            <a-collapse v-if="showSettings" v-model:activeKey="settingsActiveKey">
              <a-collapse-panel key="1" header="DeepSeek API配置">
                <a-form-item label="API密钥">
                  <a-input-password
                    v-model:value="apiKey"
                    placeholder="请输入DeepSeek API密钥"
                    :max-length="100"
                  />
                </a-form-item>
                <a-alert
                  message="您的API密钥将安全地存储在浏览器本地存储中"
                  type="info"
                  show-icon
                />
              </a-collapse-panel>
            </a-collapse>

            <!-- 分析需求输入 -->
            <a-form-item label="分析需求">
              <a-textarea
                v-model:value="analysisRequirement"
                :placeholder="getTemplatePlaceholder()"
                :rows="4"
                :max-length="1000"
                show-count
              />
            </a-form-item>

            <!-- 数据预览 -->
            <div v-if="excelData" class="data-preview">
              <a-divider>数据预览</a-divider>
              <a-descriptions :column="3" size="small" bordered>
                <a-descriptions-item label="工作表">{{ excelData.sheetName }}</a-descriptions-item>
                <a-descriptions-item label="列数">{{ excelData.headers.length }}</a-descriptions-item>
                <a-descriptions-item label="行数">{{ excelData.totalRows }}</a-descriptions-item>
              </a-descriptions>
              
              <a-table
                v-if="excelData.headers.length > 0"
                :dataSource="previewData"
                :columns="previewColumns"
                :pagination="{ pageSize: 5 }"
                size="small"
                style="margin-top: 16px"
                :scroll="{ x: 'max-content' }"
              />
            </div>

            <!-- 进度和错误 -->
            <a-progress
              v-if="isAnalyzing"
              :percent="progress"
              :status="progress === 100 ? 'success' : 'normal'"
            />
            <a-alert
              v-if="errorMessage"
              :message="errorMessage"
              type="error"
              show-icon
              closable
              @close="errorMessage = ''"
            />

            <!-- 提交按钮 -->
            <a-form-item>
              <a-button
                type="primary"
                :loading="loading || isAnalyzing"
                :disabled="!canSubmit"
                @click="handleSubmit"
                size="large"
                block
              >
                <template #icon>
                  <play-circle-outlined v-if="!loading && !isAnalyzing" />
                  <loading-outlined v-else />
                </template>
                {{ isAnalyzing ? '分析中...' : '开始分析' }}
              </a-button>
            </a-form-item>
          </a-form>
        </a-col>

        <!-- 右侧图表面板 -->
        <a-col :span="8" v-if="showCharts">
          <a-card title="数据可视化" size="small">
            <div id="chart-container" style="height: 400px; width: 100%;"></div>
            <a-empty v-if="!chartData" description="请先上传数据并选择图表类型" />
          </a-card>
        </a-col>
      </a-row>

      <!-- 历史记录抽屉 -->
      <a-drawer
        v-model:open="showHistory"
        title="分析历史记录"
        placement="right"
        :width="400"
      >
            <a-list
              v-if="analysisHistory.length > 0"
              :dataSource="analysisHistory"
            >
              <template #renderItem="{ item }">
                <a-list-item>
                  <a-list-item-meta
                    :title="item.fileName"
                    :description="`${item.type} - ${item.date}`"
                  />
                  <template #actions>
                    <a-button type="link" size="small">查看</a-button>
                  </template>
                </a-list-item>
              </template>
            </a-list>
        <a-empty v-else description="暂无历史记录" />
      </a-drawer>

      <!-- 模板库抽屉 -->
      <a-drawer
        v-model:open="showTemplates"
        title="分析模板库"
        placement="right"
        :width="400"
      >
        <a-list :dataSource="analysisTemplates">
          <template #renderItem="{ item }">
            <a-list-item @click="selectTemplate(item.key)" style="cursor: pointer;">
              <a-list-item-meta
                :title="item.name"
                :description="item.description"
              />
            </a-list-item>
          </template>
        </a-list>
      </a-drawer>

      <!-- 批量分析抽屉 -->
      <a-drawer
        v-model:open="showBatch"
        title="批量分析"
        placement="right"
        :width="400"
      >
        <a-upload
          :file-list="batchFiles"
          :before-upload="handleBatchUpload"
          :remove="handleBatchRemove"
          accept=".xlsx,.xls"
          multiple
          :max-count="5"
        >
          <a-button>
            <upload-outlined />
            选择多个文件
          </a-button>
        </a-upload>
        <a-button
          type="primary"
          :disabled="batchFiles.length === 0"
          @click="startBatchAnalysis"
          style="margin-top: 16px"
          block
        >
          开始批量分析
        </a-button>
      </a-drawer>

      <!-- 分析结果 -->
      <div v-if="analysisResult" class="result-section">
        <a-divider />
        <div class="result-header">
          <h3>分析结果</h3>
          <a-space>
            <a-button @click="downloadResult" type="default" size="small">
              <download-outlined />
              下载报告
            </a-button>
            <a-button @click="saveToHistory" type="primary" size="small">
              保存到历史
            </a-button>
          </a-space>
        </div>
        <div class="markdown-content" v-html="renderedMarkdown"></div>
      </div>
    </a-card>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { message } from 'ant-design-vue'
import { UploadOutlined, DownloadOutlined, SettingOutlined, PlayCircleOutlined, LoadingOutlined, HistoryOutlined, BarChartOutlined, FolderOutlined, InboxOutlined } from '@ant-design/icons-vue'
import { marked } from 'marked'
import * as XLSX from 'xlsx'
import * as echarts from 'echarts'

// 状态管理
const fileList = ref([])
const analysisRequirement = ref('')
const loading = ref(false)
const analysisResult = ref('')
const excelData = ref(null)
const analysisType = ref('general')
const apiKey = ref('')
const showSettings = ref(false)
const showHistory = ref(false)
const showTemplates = ref(false)
const showCharts = ref(false)
const showBatch = ref(false)
const enableBatchUpload = ref(false)
const errorMessage = ref('')
const isAnalyzing = ref(false)
const progress = ref(0)
const settingsActiveKey = ref(['1'])
const batchFiles = ref([])
const analysisHistory = ref([])
const selectedTemplate = ref('')
const chartType = ref('bar')
const chartInstance = ref(null)
const chartData = ref(null)

// 分析模板
const analysisTemplates = [
  {
    key: 'sales',
    name: '销售数据分析',
    description: '分析销售趋势、产品表现和客户行为',
    prompt: '请分析销售数据，包括：1) 月度销售趋势 2) 产品类别表现 3) 客户购买行为 4) 销售预测建议'
  },
  {
    key: 'finance',
    name: '财务报表分析',
    description: '分析收入、支出、利润和现金流',
    prompt: '请分析财务数据，包括：1) 收入支出结构 2) 利润率变化 3) 成本控制 4) 财务健康度评估'
  },
  {
    key: 'hr',
    name: '人力资源分析',
    description: '分析员工绩效、离职率和薪酬结构',
    prompt: '请分析人力资源数据，包括：1) 员工绩效分布 2) 离职率分析 3) 薪酬结构合理性 4) 人才发展建议'
  },
  {
    key: 'inventory',
    name: '库存管理分析',
    description: '分析库存周转、缺货和积压情况',
    prompt: '请分析库存数据，包括：1) 库存周转率 2) 缺货风险 3) 积压商品 4) 采购优化建议'
  },
  {
    key: 'customer',
    name: '客户行为分析',
    description: '分析客户价值、留存率和满意度',
    prompt: '请分析客户数据，包括：1) 客户价值分层 2) 留存率分析 3) 满意度评估 4) 客户生命周期管理'
  }
]

// 计算属性
const canSubmit = computed(() => {
  return fileList.value.length > 0 && analysisRequirement.value.trim() && !loading.value && !isAnalyzing.value
})

const renderedMarkdown = computed(() => {
  if (!analysisResult.value) return ''
  return marked(analysisResult.value)
})

const previewData = computed(() => {
  if (!excelData.value) return []
  return excelData.value.rows.slice(0, 5).map((row, index) => {
    const obj = { key: index }
    excelData.value.headers.forEach((header, i) => {
      obj[header] = row[i] || ''
    })
    return obj
  })
})

const previewColumns = computed(() => {
  if (!excelData.value) return []
  return excelData.value.headers.map(header => ({
    title: header,
    dataIndex: header,
    key: header,
    width: 150,
    ellipsis: true
  }))
})

// 生命周期
onMounted(() => {
  // 从本地存储加载数据
  const savedApiKey = localStorage.getItem('deepseek_api_key')
  const savedHistory = localStorage.getItem('analysis_history')
  
  if (savedApiKey) apiKey.value = savedApiKey
  if (savedHistory) analysisHistory.value = JSON.parse(savedHistory)
})

// 监听变化
watch(apiKey, (newKey) => {
  if (newKey) localStorage.setItem('deepseek_api_key', newKey)
  else localStorage.removeItem('deepseek_api_key')
})

watch(analysisHistory, (newHistory) => {
  localStorage.setItem('analysis_history', JSON.stringify(newHistory))
}, { deep: true })

// 文件上传处理
const beforeUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  if (enableBatchUpload.value) {
    fileList.value = [...fileList.value, file]
  } else {
    fileList.value = [file]
  }
  
  readExcelFile(file)
  return false
}

const handleRemove = (file) => {
  const index = fileList.value.indexOf(file)
  if (index > -1) {
    fileList.value.splice(index, 1)
  }
  if (fileList.value.length === 0) {
    excelData.value = null
  }
}

// 批量处理
const handleBatchUpload = (file) => {
  const isExcel = file.type === 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' || 
                  file.type === 'application/vnd.ms-excel'
  
  if (!isExcel) {
    message.error('只能上传Excel文件!')
    return false
  }
  
  const isLt10M = file.size / 1024 / 1024 < 10
  if (!isLt10M) {
    message.error('文件大小不能超过10MB!')
    return false
  }
  
  batchFiles.value = [...batchFiles.value, file]
  return false
}

const handleBatchRemove = (file) => {
  const index = batchFiles.value.indexOf(file)
  if (index > -1) {
    batchFiles.value.splice(index, 1)
  }
}

const startBatchAnalysis = async () => {
  if (batchFiles.value.length === 0) {
    message.warning('请先选择文件')
    return
  }

  for (const file of batchFiles.value) {
    await processSingleFile(file)
  }
  
  message.success('批量分析完成!')
  batchFiles.value = []
  showBatch.value = false
}

const processSingleFile = async (file) => {
  return new Promise((resolve) => {
    const reader = new FileReader()
    reader.onload = async (e) => {
      try {
        const data = new Uint8Array(e.target.result)
        const workbook = XLSX.read(data, { type: 'array' })
        const firstSheetName = workbook.SheetNames[0]
        const worksheet = workbook.Sheets[firstSheetName]
        const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
        
        // 这里可以添加批量分析逻辑
        message.success(`已处理: ${file.name}`)
        resolve()
      } catch (error) {
        message.error(`处理失败: ${file.name}`)
        resolve()
      }
    }
    reader.readAsArrayBuffer(file)
  })
}

// 读取Excel文件
const readExcelFile = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    try {
      const data = new Uint8Array(e.target.result)
      const workbook = XLSX.read(data, { type: 'array' })
      
      const firstSheetName = workbook.SheetNames[0]
      const worksheet = workbook.Sheets[firstSheetName]
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 })
      
      excelData.value = {
        sheetName: firstSheetName,
        headers: jsonData[0] || [],
        rows: jsonData.slice(1),
        totalRows: jsonData.length - 1
      }
      
      // 初始化图表数据
      initChartData()
      
      message.success('文件读取成功!')
    } catch (error) {
      message.error('文件读取失败: ' + error.message)
      console.error('Excel读取错误:', error)
    }
  }
  reader.readAsArrayBuffer(file)
}

// 模板应用
const selectTemplate = (templateKey) => {
  const template = analysisTemplates.find(t => t.key === templateKey)
  if (template) {
    selectedTemplate.value = templateKey
    analysisRequirement.value = template.prompt
    analysisType.value = templateKey === 'trend' ? 'trend' : 
                        templateKey === 'statistics' ? 'statistics' : 'general'
  }
}

const getTemplatePlaceholder = () => {
  if (selectedTemplate.value) {
    const template = analysisTemplates.find(t => t.key === selectedTemplate.value)
    return template ? template.prompt : '请输入您的分析需求...'
  }
  return '请输入您的分析需求，例如：分析销售数据的趋势、统计各产品的销售额等'
}

// 智能图表数据检测
const initChartData = () => {
  if (!excelData.value) return
  
  // 智能检测数值列 - 自动识别数字类型
  const numericColumns = excelData.value.headers.filter((header, index) => {
    // 检查前5行数据，如果大部分是数字则认为是数值列
    const sampleValues = excelData.value.rows.slice(0, 5).map(row => {
      const value = row[index]
      return value !== null && value !== undefined && value !== '' ? String(value) : ''
    })
    
    const numericCount = sampleValues.filter(val => {
      const num = parseFloat(val)
      return !isNaN(num) && isFinite(num)
    }).length
    
    return numericCount >= 3 // 至少3个有效数值
  })
  
  if (numericColumns.length > 0) {
    chartData.value = {
      columns: numericColumns,
      data: excelData.value.rows.slice(0, 20).map((row, index) => {
        const item = { name: `第${index + 1}行` }
        numericColumns.forEach(col => {
          const colIndex = excelData.value.headers.indexOf(col)
          const value = row[colIndex]
          item[col] = value !== null && value !== undefined && value !== '' ? 
            (isNaN(parseFloat(value)) ? 0 : parseFloat(value)) : 0
        })
        return item
      })
    }
    
    nextTick(() => {
      renderChart()
    })
  } else {
    chartData.value = null
  }
}

const renderChart = () => {
  if (!chartData.value || !showCharts.value) return
  
  const container = document.querySelector('#chart-container')
  if (!container) return
  
  if (chartInstance.value) {
    chartInstance.value.dispose()
  }
  
  chartInstance.value = echarts.init(container)
  
  // 根据图表类型生成不同的配置
  let option = {}
  
  if (chartType.value === 'pie') {
    // 饼图配置 - 使用第一列数值列的数据
    const firstCol = chartData.value.columns[0]
    const pieData = chartData.value.data.map((item, index) => ({
      name: `第${index + 1}行`,
      value: item[firstCol] || 0
    }))
    
    option = {
      title: {
        text: `${firstCol} - 饼图`
      },
      tooltip: {
        trigger: 'item',
        formatter: '{a} <br/>{b}: {c} ({d}%)'
      },
      legend: {
        orient: 'vertical',
        left: 'left',
        data: pieData.map(item => item.name),
        selected: pieData.reduce((acc, item) => {
          acc[item.name] = true
          return acc
        }, {}) // 防止legend点击报错，初始化所有项为选中状态
      },
      series: [
        {
          name: firstCol,
          type: 'pie',
          radius: '50%',
          data: pieData,
          emphasis: {
            itemStyle: {
              shadowBlur: 10,
              shadowOffsetX: 0,
              shadowColor: 'rgba(0, 0, 0, 0.5)'
            }
          }
        }
      ]
    }
  } else if (chartType.value === 'scatter') {
    // 散点图配置 - 使用前两个数值列
    const col1 = chartData.value.columns[0]
    const col2 = chartData.value.columns[1] || chartData.value.columns[0]

    option = {
      title: {
        text: `${col1} vs ${col2} - 散点图`
      },
      tooltip: {
        trigger: 'item',
        formatter: function(params) {
          return `${params.data[0]}, ${params.data[1]}`
        }
      },
      legend: {
        data: ['散点'],
        selected: { '散点': true } // 防止legend点击报错
      },
      xAxis: {
        type: 'value',
        name: col1
      },
      yAxis: {
        type: 'value',
        name: col2
      },
      series: [
        {
          name: '散点',
          type: 'scatter',
          data: chartData.value.data.map(item => [item[col1] || 0, item[col2] || 0]),
          symbolSize: 8
        }
      ]
    }
  } else {
    // 柱状图和折线图配置
    option = {
      title: {
        text: '数据可视化'
      },
      tooltip: {
        trigger: 'axis'
      },
      legend: {
        data: chartData.value.columns || [],
        selected: chartData.value.columns ? chartData.value.columns.reduce((acc, col) => {
          acc[col] = true
          return acc
        }, {}) : {} // 防止legend点击报错，初始化所有项为选中状态
      },
      xAxis: {
        type: 'category',
        data: chartData.value.data.map((_, index) => `第${index + 1}行`)
      },
      yAxis: {
        type: 'value'
      },
      series: chartData.value.columns.map(col => ({
        name: col,
        type: chartType.value,
        data: chartData.value.data.map(item => item[col]),
        smooth: chartType.value === 'line'
      }))
    }
  }
  
  // 设置图表配置
  try {
    chartInstance.value.setOption(option)

    // 添加legend事件处理，防止报错
    chartInstance.value.on('legendselectchanged', function(params) {
      try {
        // 处理legend选择变化事件
        console.log('Legend selection changed:', params)
        // 这里可以添加自定义的legend处理逻辑
      } catch (error) {
        console.warn('Legend select changed error:', error)
      }
    })

    // 添加其他可能的legend事件处理
    chartInstance.value.on('legendselected', function(params) {
      try {
        console.log('Legend selected:', params)
      } catch (error) {
        console.warn('Legend selected error:', error)
      }
    })

    chartInstance.value.on('legendunselected', function(params) {
      try {
        console.log('Legend unselected:', params)
      } catch (error) {
        console.warn('Legend unselected error:', error)
      }
    })

  } catch (error) {
    console.error('Chart rendering error:', error)
    message.error('图表渲染失败: ' + error.message)
  }
}

// 保存到历史
const saveToHistory = () => {
  if (!analysisResult.value || !excelData.value) return
  
  const historyItem = {
    id: Date.now(),
    fileName: fileList.value[0]?.name || '未知文件',
    date: new Date().toLocaleString('zh-CN'),
    type: getAnalysisTypeText(analysisType.value),
    result: analysisResult.value,
    dataSummary: {
      sheetName: excelData.value.sheetName,
      headers: excelData.value.headers,
      totalRows: excelData.value.totalRows
    }
  }
  
  analysisHistory.value.unshift(historyItem)
  if (analysisHistory.value.length > 50) {
    analysisHistory.value = analysisHistory.value.slice(0, 50)
  }
  
  message.success('已保存到历史记录')
}

// 生命周期增强
onMounted(() => {
  // 加载所有本地存储数据
  const savedApiKey = localStorage.getItem('deepseek_api_key')
  const savedHistory = localStorage.getItem('analysis_history')
  const savedTemplates = localStorage.getItem('custom_templates')
  
  if (savedApiKey) apiKey.value = savedApiKey
  if (savedHistory) analysisHistory.value = JSON.parse(savedHistory)
  if (savedTemplates) {
    const customTemplates = JSON.parse(savedTemplates)
    analysisTemplates.push(...customTemplates)
  }
})

// 监听变化
watch([apiKey, analysisHistory], () => {
  localStorage.setItem('deepseek_api_key', apiKey.value)
  localStorage.setItem('analysis_history', JSON.stringify(analysisHistory.value))
}, { deep: true })

// 监听图表类型变化，实现实时切换
watch(chartType, () => {
  if (chartData.value && showCharts.value) {
    renderChart()
  }
})

// 监听图表显示状态变化，实现动态渲染
watch(showCharts, (newVal) => {
  if (newVal && chartData.value) {
    nextTick(() => {
      renderChart()
    })
  }
})

// 提交分析
const handleSubmit = async () => {
  if (!excelData.value || !analysisRequirement.value.trim()) {
    message.warning('请上传文件并输入分析需求')
    return
  }

  if (!apiKey.value.trim()) {
    message.warning('请先配置DeepSeek API密钥')
    showSettings.value = true
    return
  }

  isAnalyzing.value = true
  errorMessage.value = ''
  progress.value = 0
  
  try {
    const progressInterval = setInterval(() => {
      if (progress.value < 90) {
        progress.value += 10
      }
    }, 200)

    const result = await callDeepSeekAPI()
    
    clearInterval(progressInterval)
    progress.value = 100
    
    analysisResult.value = result
    message.success('分析完成!')
    
    // 自动保存到历史
    saveToHistory()
  } catch (error) {
    errorMessage.value = error.message || '分析失败，请重试'
    console.error('API调用错误:', error)
  } finally {
    isAnalyzing.value = false
    setTimeout(() => {
      progress.value = 0
    }, 1000)
  }
}

// 真实的DeepSeek API调用
const callDeepSeekAPI = async () => {
  // 准备数据
  const dataSummary = {
    sheetName: excelData.value.sheetName,
    headers: excelData.value.headers,
    totalRows: excelData.value.totalRows,
    sampleData: excelData.value.rows.slice(0, 20).map(row => 
      excelData.value.headers.reduce((obj, header, index) => {
        obj[header] = row[index] || null
        return obj
      }, {})
    )
  }

  const prompt = `请作为数据分析师，分析以下Excel数据，根据用户需求提供专业分析报告。

## 分析要求
- 分析类型：${getAnalysisTypeText(analysisType.value)}
- 用户需求：${analysisRequirement.value}

## 数据结构
- 工作表：${dataSummary.sheetName}
- 列数：${dataSummary.headers.length}
- 行数：${dataSummary.totalRows}
- 列名：${dataSummary.headers.join(' | ')}

## 数据样本（前20行）
\`\`\`json
${JSON.stringify(dataSummary.sampleData, null, 2)}
\`\`\`

## 分析任务
请提供以下内容的详细分析：
1. 数据概览和基本统计信息
2. 针对用户具体需求的深度分析
3. 关键业务洞察和数据模式
4. 数据质量评估（缺失值、异常值等）
5. 基于分析结果的具体行动建议
6. 后续分析方向推荐

请使用清晰的Markdown格式，包含表格、列表和适当的格式化。`

  try {
    const response = await fetch('https://api.deepseek.com/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${apiKey.value.trim()}`
      },
      body: JSON.stringify({
        model: 'deepseek-chat',
        messages: [
          {
            role: 'system',
            content: '你是一个专业的数据分析师，擅长Excel数据分析和商业洞察。请提供准确、实用的分析报告。'
          },
          {
            role: 'user',
            content: prompt
          }
        ],
        max_tokens: 3000,
        temperature: 0.7,
        stream: false
      })
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(`API调用失败: ${errorData.error?.message || response.statusText}`)
    }

    const data = await response.json()
    return data.choices[0]?.message?.content || '未能获取分析结果'
  } catch (error) {
    console.error('DeepSeek API调用错误:', error)
    throw error
  }
}

const getAnalysisTypeText = (type) => {
  const typeMap = {
    general: '通用分析',
    trend: '趋势分析',
    statistics: '统计分析',
    correlation: '相关性分析',
    anomaly: '异常检测'
  }
  return typeMap[type] || '通用分析'
}

// 下载结果
const downloadResult = () => {
  if (!analysisResult.value) return
  
  const blob = new Blob([analysisResult.value], { type: 'text/markdown' })
  const url = URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.href = url
  a.download = `excel-analysis-${new Date().getTime()}.md`
  document.body.appendChild(a)
  a.click()
  document.body.removeChild(a)
  
  message.success('下载成功!')
}
</script>

<style scoped>
.analyse-doc-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
}

.analyse-card {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-radius: 12px;
}

.feature-nav {
  margin-bottom: 24px;
}

.data-preview {
  margin: 16px 0;
}

.chart-container {
  height: 400px;
  border: 1px solid #e8e8e8;
  border-radius: 8px;
}

@media (max-width: 768px) {
  .analyse-doc-container {
    padding: 12px;
  }
}
</style>
